const express = require("express");
const {
  modifyContact,
  getContactDetails,
  searchContacts,
  getDefaultContact,
  associateExtraDetails,
  deleteContact,
  createCustomer,
  addContact,
} = require("../controllers/dn-management/contactController");

const contactRouter = express.Router();

contactRouter.post("/add", addContact);

contactRouter.put("/modify", modifyContact);

contactRouter.get("/search", searchContacts);

contactRouter.get("/:contactId", getContactDetails);

contactRouter.get("/default/:customerId", getDefaultContact);

contactRouter.post("/associate-extra", associateExtraDetails);

contactRouter.delete("/:contactId", deleteContact);

contactRouter.post("/customer/create", createCustomer);

module.exports = contactRouter;
