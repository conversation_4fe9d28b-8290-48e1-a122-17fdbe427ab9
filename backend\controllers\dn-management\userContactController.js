const User = require("../../models/User");
const Contact = require("../../models/Contact");
const contactService = require("../../services/contactService");

/**
 * Get user's domain contacts
 */
exports.getUserDomainContacts = async (req, res) => {
  try {
    const userId = req.user._id;
    const user = await User.findById(userId).populate(
      "domainContacts.registrant domainContacts.admin domainContacts.tech domainContacts.billing"
    );

    if (!user) {
      return res.status(404).json({ error: "User not found" });
    }

    // Get existing contacts from database
    const contacts = {
      registrant: user.domainContacts.registrant,
      admin: user.domainContacts.admin,
      tech: user.domainContacts.tech,
      billing: user.domainContacts.billing,
    };

    // Helper function to convert country name to ISO code
    const getCountryCode = (countryInput) => {
      if (!countryInput) return "";

      // If already a 2-letter code, return as is
      if (countryInput.length === 2 && /^[A-Z]{2}$/i.test(countryInput)) {
        return countryInput.toUpperCase();
      }

      // Common country name to ISO code mappings
      const countryMappings = {
        morocco: "MA",
        maroc: "MA",
        france: "FR",
        "united states": "US",
        usa: "US",
        canada: "CA",
        "united kingdom": "GB",
        uk: "GB",
        spain: "ES",
        germany: "DE",
        italy: "IT",
        portugal: "PT",
        algeria: "DZ",
        tunisia: "TN",
        egypt: "EG",
        "saudi arabia": "SA",
        uae: "AE",
        "united arab emirates": "AE",
        netherlands: "NL",
        belgium: "BE",
        switzerland: "CH",
        austria: "AT",
        sweden: "SE",
        norway: "NO",
        denmark: "DK",
        finland: "FI",
        poland: "PL",
        "czech republic": "CZ",
        hungary: "HU",
        romania: "RO",
        bulgaria: "BG",
        greece: "GR",
        turkey: "TR",
        russia: "RU",
        ukraine: "UA",
        india: "IN",
        china: "CN",
        japan: "JP",
        "south korea": "KR",
        australia: "AU",
        "new zealand": "NZ",
        brazil: "BR",
        argentina: "AR",
        mexico: "MX",
        chile: "CL",
        colombia: "CO",
        peru: "PE",
        venezuela: "VE",
        "south africa": "ZA",
        nigeria: "NG",
        kenya: "KE",
        ghana: "GH",
        israel: "IL",
        lebanon: "LB",
        jordan: "JO",
        kuwait: "KW",
        qatar: "QA",
        bahrain: "BH",
        oman: "OM",
      };

      const normalizedInput = countryInput.toLowerCase().trim();
      return (
        countryMappings[normalizedInput] ||
        countryInput.toUpperCase().substring(0, 2)
      );
    };

    // Helper function to validate and format zipcode
    const formatZipcode = (zipcode, countryCode) => {
      if (!zipcode) return "";

      // Remove spaces and special characters, keep only alphanumeric
      let cleanZip = zipcode.toString().replace(/[^a-zA-Z0-9]/g, "");

      // Country-specific zipcode formatting
      switch (countryCode) {
        case "US":
          // US: 5 digits or 5+4 format
          if (cleanZip.length >= 5) {
            return cleanZip.substring(0, 5);
          }
          break;
        case "CA":
          // Canada: A1A 1A1 format
          if (cleanZip.length >= 6) {
            return cleanZip.substring(0, 6).toUpperCase();
          }
          break;
        case "GB":
          // UK: Various formats, keep original but clean
          return zipcode
            .toString()
            .replace(/[^a-zA-Z0-9\s]/g, "")
            .trim()
            .toUpperCase();
        case "FR":
          // France: 5 digits
          if (cleanZip.length >= 5) {
            return cleanZip.substring(0, 5);
          }
          break;
        case "DE":
          // Germany: 5 digits
          if (cleanZip.length >= 5) {
            return cleanZip.substring(0, 5);
          }
          break;
        case "MA":
          // Morocco: 5 digits
          if (cleanZip.length >= 5) {
            return cleanZip.substring(0, 5);
          }
          break;
        default:
          // For other countries, return cleaned version with max 10 chars
          return cleanZip.substring(0, 10);
      }

      return cleanZip;
    };

    // Prepare user info for prepopulation
    const userCountryCode = getCountryCode(user.billingInfo?.country || "");
    const userZipcode = formatZipcode(
      user.billingInfo?.zipCode || user.billingInfo?.zipcode || "",
      userCountryCode
    );

    const userInfo = {
      name: `${user.firstName} ${user.lastName}`.trim(),
      email: user.email,
      company: user.billingInfo?.companyName || "",
      address: user.billingInfo?.address || "",
      city: user.billingInfo?.city || "",
      country: userCountryCode,
      zipcode: userZipcode,
      phoneCountryCode: user.phoneCountryCode || "",
      phone: user.phone || "",
    };

    // If no contacts exist, provide user info as default for prepopulation
    const contactsWithDefaults = {};
    const contactTypes = ["registrant", "admin", "tech", "billing"];

    contactTypes.forEach((type) => {
      if (contacts[type]) {
        contactsWithDefaults[type] = contacts[type];
      } else {
        // Prepopulate with user info if no contact exists
        contactsWithDefaults[type] = {
          ...userInfo,
          contactType: type,
          isDefault: true, // Flag to indicate this is prepopulated data
        };
      }
    });

    res.json({
      success: true,
      contacts: contactsWithDefaults,
      userInfo, // Send user info separately for reference
    });
  } catch (error) {
    console.error("Error getting user domain contacts:", error);
    res.status(500).json({ error: "Failed to get domain contacts" });
  }
};

/**
 * Create or update user's domain contact
 */
exports.createOrUpdateDomainContact = async (req, res) => {
  try {
    const userId = req.user._id;
    const { contactType, contactDetails } = req.body;
    const customerId = process.env.COMPANY_CUSTOMER_ID;
    // Validate contact type
    const validTypes = ["registrant", "admin", "tech", "billing"];
    if (!validTypes.includes(contactType)) {
      return res.status(400).json({ error: "Invalid contact type" });
    }

    // Validate required fields
    if (!contactDetails || !contactDetails.name || !contactDetails.email) {
      return res.status(400).json({
        error: "Contact details with name and email are required",
      });
    }

    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ error: "User not found" });
    }

    // Validate and format contact details
    const validatedContactDetails = {
      ...contactDetails,
      country: getCountryCode(contactDetails.country || ""),
      zipcode: formatZipcode(
        contactDetails.zipcode || "",
        getCountryCode(contactDetails.country || "")
      ),
      // Ensure phone country code is numeric
      phoneCountryCode: (
        contactDetails.phoneCountryCode ||
        contactDetails.phoneCc ||
        ""
      ).replace(/[^0-9]/g, ""),
      // Ensure phone is numeric
      phone: (contactDetails.phone || "").replace(/[^0-9]/g, ""),
    };

    // Additional validation
    if (!validatedContactDetails.country) {
      return res.status(400).json({
        error: "Valid country code is required (e.g., US, CA, FR, MA)",
      });
    }

    if (
      validatedContactDetails.zipcode &&
      validatedContactDetails.zipcode.length < 3
    ) {
      return res.status(400).json({
        error: "Valid postal code is required for the selected country",
      });
    }

    if (
      validatedContactDetails.phoneCountryCode &&
      (validatedContactDetails.phoneCountryCode.length < 1 ||
        validatedContactDetails.phoneCountryCode.length > 3)
    ) {
      return res.status(400).json({
        error: "Phone country code must be 1-3 digits",
      });
    }

    if (
      validatedContactDetails.phone &&
      (validatedContactDetails.phone.length < 4 ||
        validatedContactDetails.phone.length > 12)
    ) {
      return res.status(400).json({
        error: "Phone number must be 4-12 digits",
      });
    }

    // Check if contact already exists in database
    let existingContact = await Contact.findByUserAndType(userId, contactType);

    // Use provided customerId or create one if needed
    let customerIdToUse = customerId;
    if (!customerIdToUse) {
      // Create customer if not provided
      const customerDetails = {
        name: validatedContactDetails.name,
        email: validatedContactDetails.email,
        company: validatedContactDetails.company || "",
        address:
          validatedContactDetails.address ||
          validatedContactDetails.addressLine1 ||
          "",
        city: validatedContactDetails.city || "",
        country: validatedContactDetails.country,
        zipcode: validatedContactDetails.zipcode,
        phoneCountryCode: validatedContactDetails.phoneCountryCode,
        phone: validatedContactDetails.phone,
      };

      try {
        customerIdToUse = await contactService.createCustomer(customerDetails);
      } catch (customerError) {
        console.log(
          "Customer creation failed, using fallback approach:",
          customerError.message
        );
        // Use a fallback customer ID or handle gracefully
        customerIdToUse = `fallback-${userId}-${Date.now()}`;
      }
    }

    let contactId;
    let dbContact;

    if (existingContact) {
      // Update existing contact in external API
      await contactService.modifyContact(
        existingContact.externalContactId,
        validatedContactDetails
      );
      contactId = existingContact.externalContactId;

      // Update contact in database
      existingContact.updateContactData(validatedContactDetails);
      existingContact.customerId = customerIdToUse;
      await existingContact.save();
      dbContact = existingContact;
    } else {
      // Create new contact in external API
      const result = await contactService.addContact(
        validatedContactDetails,
        customerIdToUse
      );
      contactId = result.contactId || result["contact-id"];

      // Create contact in database
      dbContact = new Contact({
        userId,
        externalContactId: contactId,
        contactType,
        name: validatedContactDetails.name,
        email: validatedContactDetails.email,
        company: validatedContactDetails.company || "",
        address: validatedContactDetails.address || "",
        city: validatedContactDetails.city || "",
        country: validatedContactDetails.country,
        zipcode: validatedContactDetails.zipcode,
        phoneCountryCode: validatedContactDetails.phoneCountryCode,
        phone: validatedContactDetails.phone,
        customerId: customerIdToUse,
      });
      await dbContact.save();
    }

    // Update user's contact reference
    user.domainContacts[contactType] = dbContact._id;
    await user.save();

    res.json({
      success: true,
      message: `${contactType} contact ${
        existingContact ? "updated" : "created"
      } successfully`,
      contactId,
      contact: dbContact,
      customerId: customerIdToUse,
    });
  } catch (error) {
    console.error("Error creating/updating domain contact:", error);
    res.status(500).json({
      error: "Failed to create/update domain contact",
      details: error.response?.data || error.message,
    });
  }
};

/**
 * Delete user's domain contact
 */
exports.deleteDomainContact = async (req, res) => {
  try {
    const userId = req.user._id;
    const { contactType } = req.params;

    // Validate contact type
    const validTypes = ["registrant", "admin", "tech", "billing"];
    if (!validTypes.includes(contactType)) {
      return res.status(400).json({ error: "Invalid contact type" });
    }

    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ error: "User not found" });
    }

    // Find contact in database
    const contact = await Contact.findByUserAndType(userId, contactType);
    if (!contact) {
      return res.status(404).json({ error: "Contact not found" });
    }

    // Delete contact from external service
    await contactService.deleteContact(contact.externalContactId);

    // Mark contact as deleted in database
    contact.status = "deleted";
    await contact.save();

    // Remove contact reference from user
    user.domainContacts[contactType] = null;
    await user.save();

    res.json({
      success: true,
      message: `${contactType} contact deleted successfully`,
    });
  } catch (error) {
    console.error("Error deleting domain contact:", error);
    res.status(500).json({
      error: "Failed to delete domain contact",
      details: error.response?.data || error.message,
    });
  }
};

/**
 * Copy contact details from one type to another
 */
exports.copyDomainContact = async (req, res) => {
  try {
    const userId = req.user._id;
    const { fromType, toType, customerId } = req.body;

    // Validate contact types
    const validTypes = ["registrant", "admin", "tech", "billing"];
    if (!validTypes.includes(fromType) || !validTypes.includes(toType)) {
      return res.status(400).json({ error: "Invalid contact type" });
    }

    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ error: "User not found" });
    }

    // Find source contact in database
    const sourceContact = await Contact.findByUserAndType(userId, fromType);
    if (!sourceContact) {
      return res
        .status(404)
        .json({ error: `Source ${fromType} contact not found` });
    }

    // Check if target contact already exists
    let targetContact = await Contact.findByUserAndType(userId, toType);

    // Get source contact API data and validate it
    const rawContactData = sourceContact.getApiData();
    const validatedContactData = {
      ...rawContactData,
      country: getCountryCode(rawContactData.country || ""),
      zipcode: formatZipcode(
        rawContactData.zipcode || "",
        getCountryCode(rawContactData.country || "")
      ),
      phoneCountryCode: (rawContactData.phoneCountryCode || "").replace(
        /[^0-9]/g,
        ""
      ),
      phone: (rawContactData.phone || "").replace(/[^0-9]/g, ""),
    };

    let newContactId;
    let dbContact;

    if (targetContact) {
      // Update existing target contact
      await contactService.modifyContact(
        targetContact.externalContactId,
        validatedContactData
      );
      newContactId = targetContact.externalContactId;

      // Update in database
      targetContact.updateContactData(validatedContactData);
      targetContact.customerId = customerId || sourceContact.customerId;
      await targetContact.save();
      dbContact = targetContact;
    } else {
      // Create new contact in external API
      const result = await contactService.addContact(
        validatedContactData,
        customerId || sourceContact.customerId
      );
      newContactId = result.contactId || result["contact-id"];

      // Create new contact in database
      dbContact = new Contact({
        userId,
        externalContactId: newContactId,
        contactType: toType,
        name: validatedContactData.name,
        email: validatedContactData.email,
        company: validatedContactData.company,
        address: validatedContactData.address,
        city: validatedContactData.city,
        country: validatedContactData.country,
        zipcode: validatedContactData.zipcode,
        phoneCountryCode: validatedContactData.phoneCountryCode,
        phone: validatedContactData.phone,
        customerId: customerId || sourceContact.customerId,
      });
      await dbContact.save();
    }

    // Update user's contact reference
    user.domainContacts[toType] = dbContact._id;
    await user.save();

    res.json({
      success: true,
      message: `${fromType} contact copied to ${toType} successfully`,
      contactId: newContactId,
      contact: dbContact,
    });
  } catch (error) {
    console.error("Error copying domain contact:", error);
    res.status(500).json({
      error: "Failed to copy domain contact",
      details: error.response?.data || error.message,
    });
  }
};
