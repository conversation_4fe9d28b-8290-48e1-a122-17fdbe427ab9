const User = require("../../models/User");
const Contact = require("../../models/Contact");
const contactService = require("../../services/contactService");

/**
 * Get user's domain contacts
 */
exports.getUserDomainContacts = async (req, res) => {
  try {
    const userId = req.user._id;
    const user = await User.findById(userId).populate(
      "domainContacts.registrant domainContacts.admin domainContacts.tech domainContacts.billing"
    );

    if (!user) {
      return res.status(404).json({ error: "User not found" });
    }

    // Get existing contacts from database
    const contacts = {
      registrant: user.domainContacts.registrant,
      admin: user.domainContacts.admin,
      tech: user.domainContacts.tech,
      billing: user.domainContacts.billing,
    };

    // Prepare user info for prepopulation
    const userInfo = {
      name: `${user.firstName} ${user.lastName}`.trim(),
      email: user.email,
      company: user.billingInfo?.companyName || "",
      address: user.billingInfo?.address || "",
      city: user.billingInfo?.city || "",
      country: user.billingInfo?.country || "",
      zipcode: user.billingInfo?.zipCode || "",
      phoneCountryCode: user.phoneCountryCode || "",
      phone: user.phone || "",
    };

    // If no contacts exist, provide user info as default for prepopulation
    const contactsWithDefaults = {};
    const contactTypes = ["registrant", "admin", "tech", "billing"];

    contactTypes.forEach((type) => {
      if (contacts[type]) {
        contactsWithDefaults[type] = contacts[type];
      } else {
        // Prepopulate with user info if no contact exists
        contactsWithDefaults[type] = {
          ...userInfo,
          contactType: type,
          isDefault: true, // Flag to indicate this is prepopulated data
        };
      }
    });

    res.json({
      success: true,
      contacts: contactsWithDefaults,
      userInfo, // Send user info separately for reference
    });
  } catch (error) {
    console.error("Error getting user domain contacts:", error);
    res.status(500).json({ error: "Failed to get domain contacts" });
  }
};

/**
 * Create or update user's domain contact
 */
exports.createOrUpdateDomainContact = async (req, res) => {
  try {
    const userId = req.user._id;
    const { contactType, contactDetails } = req.body;
const customerId=process.env.
    // Validate contact type
    const validTypes = ["registrant", "admin", "tech", "billing"];
    if (!validTypes.includes(contactType)) {
      return res.status(400).json({ error: "Invalid contact type" });
    }

    // Validate required fields
    if (!contactDetails || !contactDetails.name || !contactDetails.email) {
      return res.status(400).json({
        error: "Contact details with name and email are required",
      });
    }

    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ error: "User not found" });
    }

    // Check if contact already exists in database
    let existingContact = await Contact.findByUserAndType(userId, contactType);

    // Use provided customerId or create one if needed
    let customerIdToUse = customerId;
    if (!customerIdToUse) {
      // Create customer if not provided
      const customerDetails = {
        name: contactDetails.name,
        email: contactDetails.email,
        company: contactDetails.company || "",
        address: contactDetails.address || contactDetails.addressLine1 || "",
        city: contactDetails.city || "",
        country: contactDetails.country || "",
        zipcode: contactDetails.zipcode || "",
        phoneCountryCode:
          contactDetails.phoneCountryCode || contactDetails.phoneCc || "",
        phone: contactDetails.phone || "",
      };

      try {
        customerIdToUse = await contactService.createCustomer(customerDetails);
      } catch (customerError) {
        console.log(
          "Customer creation failed, using fallback approach:",
          customerError.message
        );
        // Use a fallback customer ID or handle gracefully
        customerIdToUse = `fallback-${userId}-${Date.now()}`;
      }
    }

    let contactId;
    let dbContact;

    if (existingContact) {
      // Update existing contact in external API
      await contactService.modifyContact(
        existingContact.externalContactId,
        contactDetails
      );
      contactId = existingContact.externalContactId;

      // Update contact in database
      existingContact.updateContactData(contactDetails);
      existingContact.customerId = customerIdToUse;
      await existingContact.save();
      dbContact = existingContact;
    } else {
      // Create new contact in external API
      const result = await contactService.addContact(
        contactDetails,
        customerIdToUse
      );
      contactId = result.contactId || result["contact-id"];

      // Create contact in database
      dbContact = new Contact({
        userId,
        externalContactId: contactId,
        contactType,
        name: contactDetails.name,
        email: contactDetails.email,
        company: contactDetails.company || "",
        address: contactDetails.address || "",
        city: contactDetails.city || "",
        country: contactDetails.country || "",
        zipcode: contactDetails.zipcode || "",
        phoneCountryCode: contactDetails.phoneCountryCode || "",
        phone: contactDetails.phone || "",
        customerId: customerIdToUse,
      });
      await dbContact.save();
    }

    // Update user's contact reference
    user.domainContacts[contactType] = dbContact._id;
    await user.save();

    res.json({
      success: true,
      message: `${contactType} contact ${
        existingContact ? "updated" : "created"
      } successfully`,
      contactId,
      contact: dbContact,
      customerId: customerIdToUse,
    });
  } catch (error) {
    console.error("Error creating/updating domain contact:", error);
    res.status(500).json({
      error: "Failed to create/update domain contact",
      details: error.response?.data || error.message,
    });
  }
};

/**
 * Delete user's domain contact
 */
exports.deleteDomainContact = async (req, res) => {
  try {
    const userId = req.user._id;
    const { contactType } = req.params;

    // Validate contact type
    const validTypes = ["registrant", "admin", "tech", "billing"];
    if (!validTypes.includes(contactType)) {
      return res.status(400).json({ error: "Invalid contact type" });
    }

    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ error: "User not found" });
    }

    // Find contact in database
    const contact = await Contact.findByUserAndType(userId, contactType);
    if (!contact) {
      return res.status(404).json({ error: "Contact not found" });
    }

    // Delete contact from external service
    await contactService.deleteContact(contact.externalContactId);

    // Mark contact as deleted in database
    contact.status = "deleted";
    await contact.save();

    // Remove contact reference from user
    user.domainContacts[contactType] = null;
    await user.save();

    res.json({
      success: true,
      message: `${contactType} contact deleted successfully`,
    });
  } catch (error) {
    console.error("Error deleting domain contact:", error);
    res.status(500).json({
      error: "Failed to delete domain contact",
      details: error.response?.data || error.message,
    });
  }
};

/**
 * Copy contact details from one type to another
 */
exports.copyDomainContact = async (req, res) => {
  try {
    const userId = req.user._id;
    const { fromType, toType, customerId } = req.body;

    // Validate contact types
    const validTypes = ["registrant", "admin", "tech", "billing"];
    if (!validTypes.includes(fromType) || !validTypes.includes(toType)) {
      return res.status(400).json({ error: "Invalid contact type" });
    }

    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ error: "User not found" });
    }

    // Find source contact in database
    const sourceContact = await Contact.findByUserAndType(userId, fromType);
    if (!sourceContact) {
      return res
        .status(404)
        .json({ error: `Source ${fromType} contact not found` });
    }

    // Check if target contact already exists
    let targetContact = await Contact.findByUserAndType(userId, toType);

    // Get source contact API data
    const contactData = sourceContact.getApiData();

    let newContactId;
    let dbContact;

    if (targetContact) {
      // Update existing target contact
      await contactService.modifyContact(
        targetContact.externalContactId,
        contactData
      );
      newContactId = targetContact.externalContactId;

      // Update in database
      targetContact.updateContactData(contactData);
      targetContact.customerId = customerId || sourceContact.customerId;
      await targetContact.save();
      dbContact = targetContact;
    } else {
      // Create new contact in external API
      const result = await contactService.addContact(
        contactData,
        customerId || sourceContact.customerId
      );
      newContactId = result.contactId || result["contact-id"];

      // Create new contact in database
      dbContact = new Contact({
        userId,
        externalContactId: newContactId,
        contactType: toType,
        name: sourceContact.name,
        email: sourceContact.email,
        company: sourceContact.company,
        address: sourceContact.address,
        city: sourceContact.city,
        country: sourceContact.country,
        zipcode: sourceContact.zipcode,
        phoneCountryCode: sourceContact.phoneCountryCode,
        phone: sourceContact.phone,
        customerId: customerId || sourceContact.customerId,
      });
      await dbContact.save();
    }

    // Update user's contact reference
    user.domainContacts[toType] = dbContact._id;
    await user.save();

    res.json({
      success: true,
      message: `${fromType} contact copied to ${toType} successfully`,
      contactId: newContactId,
      contact: dbContact,
    });
  } catch (error) {
    console.error("Error copying domain contact:", error);
    res.status(500).json({
      error: "Failed to copy domain contact",
      details: error.response?.data || error.message,
    });
  }
};
