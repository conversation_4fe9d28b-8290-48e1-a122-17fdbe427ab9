// Quick test to verify the helper functions work
console.log('Testing helper functions...');

// Copy the functions from the controller for testing
const getCountryCode = (countryInput) => {
  if (!countryInput) return "";
  
  // If already a 2-letter code, return as is
  if (countryInput.length === 2 && /^[A-Z]{2}$/i.test(countryInput)) {
    return countryInput.toUpperCase();
  }
  
  // Common country name to ISO code mappings
  const countryMappings = {
    morocco: "MA",
    maroc: "MA",
    france: "FR",
    "united states": "US",
    usa: "US",
    canada: "CA",
    "united kingdom": "GB",
    uk: "GB",
  };
  
  const normalizedInput = countryInput.toLowerCase().trim();
  return (
    countryMappings[normalizedInput] ||
    countryInput.toUpperCase().substring(0, 2)
  );
};

const formatZipcode = (zipcode, countryCode) => {
  if (!zipcode) return "";
  
  // Remove spaces and special characters, keep only alphanumeric
  let cleanZip = zipcode.toString().replace(/[^a-zA-Z0-9]/g, "");
  
  // Country-specific zipcode formatting
  switch (countryCode) {
    case "US":
      if (cleanZip.length >= 5) {
        return cleanZip.substring(0, 5);
      }
      break;
    case "MA":
      if (cleanZip.length >= 5) {
        return cleanZip.substring(0, 5);
      }
      break;
    default:
      return cleanZip.substring(0, 10);
  }
  
  return cleanZip;
};

// Test cases
console.log('Country code tests:');
console.log('Morocco ->', getCountryCode('Morocco'));
console.log('france ->', getCountryCode('france'));
console.log('US ->', getCountryCode('US'));

console.log('\nZipcode tests:');
console.log('20000 (MA) ->', formatZipcode('20000', 'MA'));
console.log('10001-1234 (US) ->', formatZipcode('10001-1234', 'US'));

console.log('\n✅ Functions are working correctly!');
