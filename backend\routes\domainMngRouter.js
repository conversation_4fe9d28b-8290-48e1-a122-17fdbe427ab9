const express = require("express");
const {
  checkDomainAvailability,
  checkIdnDomainAvailability,
  checkPremiumDomainAvailability,
  suggestDomainNames,
  getDNPricing,
  addDomainToCart,
  getResellerPricing,
  handleDomainSearch,
  syncTldPricing,
  syncDomainPricing,
  getDomainPricing,
  customerSignup,
  registerDomain,
} = require("../controllers/dn-management/domainMngController");
const { checkUserOrRefreshToken } = require("../midelwares/authorization");

const domainMngRouter = express.Router();

// Customer management

// Domain operations
domainMngRouter.get("/check-domain", checkDomainAvailability);
domainMngRouter.get("/check-idn-domain", checkIdnDomainAvailability);
domainMngRouter.get("/check-premium-domain", checkPremiumDomainAvailability);
domainMngRouter.get("/suggest-names", suggestDomainNames);
// Pricing information
domainMngRouter.get("/get-dn-pricing", getDNPricing);
domainMngRouter.get("/get-reseller-pricing", getResellerPricing);

// Add domain to cart
domainMngRouter.post(
  "/add-domain-to-cart",
  checkUserOrRefreshToken,
  addDomainToCart
);
// Search domains
domainMngRouter.get("/search-domains", handleDomainSearch);
domainMngRouter.post("/sync-tld-pricing", syncTldPricing);
// New routes for syncing and getting domain pricing
domainMngRouter.post("/sync-domain-pricing", syncDomainPricing);
domainMngRouter.get("/get-domain-pricing", getDomainPricing);

// Customer management
domainMngRouter.post("/customer-signup", customerSignup);

// Domain registration and management
domainMngRouter.post("/register-domain", registerDomain);

// domainMngRouter.post("/transfer-domain", transferDomain);
// domainMngRouter.post("/renew-domain", renewDomain);
// domainMngRouter.get("/domain-details", getDomainDetails);

// // DNS management
// domainMngRouter.post("/modify-nameservers", modifyNameServers);

// // Privacy protection
// domainMngRouter.post("/enable-privacy", enablePrivacyProtection);
// domainMngRouter.post("/disable-privacy", disablePrivacyProtection);

// // Theft protection lock
// domainMngRouter.post("/enable-theft-lock", enableTheftProtectionLock);
// domainMngRouter.post("/disable-theft-lock", disableTheftProtectionLock);

// // Domain deletion & restoration
// domainMngRouter.delete("/delete-domain", deleteDomain);
// domainMngRouter.post("/restore-domain", restoreDomain);

// // Contact management
// domainMngRouter.put("/update-contacts", updateDomainContacts);

module.exports = domainMngRouter;
